package com.example.gymbro.features.thinkingbox.domain.parser

import com.example.gymbro.features.thinkingbox.domain.model.events.SemanticEvent
import com.example.gymbro.features.thinkingbox.logging.RawTokenRecorder
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * StreamingThinkingMLParser - 纯 XML 语法解析器 v3.0
 *
 * 🎯 核心职责：
 * - XML Token 流解析：将原始 token 转换为结构化的 XML 事件
 * - 状态机管理：维护解析器状态（PRE_THINK → THINKING → POST_FINAL）
 * - 基础事件生成：只生成 TagOpened、TagClosed、TextChunk、StreamFinished 事件
 *
 * 🔥 架构原则：
 * - 纯解析器：不处理业务逻辑，不维护 phase 状态
 * - 无副作用：仅做语法解析，语义映射由 DomainMapper 负责
 * - 状态管理：维护最小必要的解析状态
 * - 错误处理：优雅处理恶意 XML 和解析错误
 *
 * 🔧 重构改进：
 * - 移除所有业务逻辑处理
 * - 简化状态机，仅用于解析上下文
 * - 优化性能，减少不必要的字符串操作
 * - 增强错误处理和调试支持
 */
@Singleton
class StreamingThinkingMLParser @Inject constructor(
    private val xmlScanner: XmlStreamScanner,
) {

    // 🔥 【RAW Token采集】ThinkingBox端的token采集器
    private var tbRawTokenCount = 0L
    private val tbRawTokenBuffer = mutableListOf<String>()
    private var tbXmlTokenCount = 0L
    private val tbXmlTokenBuffer = mutableListOf<String>()

    /**
     * 解析器状态枚举
     * 用于追踪当前解析上下文，不涉及业务逻辑
     */
    enum class ParserState {
        PRE_THINK, // 解析前：等待 <think> 或 <thinking> 标签
        THINKING, // 思考中：处理 <thinking> 块内容
        POST_FINAL, // 思考后：处理 <final> 块内容
    }

    /**
     * 解析器上下文
     * 维护解析过程中的最小必要状态
     */
    data class ParserContext(
        var state: ParserState = ParserState.PRE_THINK,
        var tokenCount: Long = 0L, // 处理的 token 计数（用于调试）
    )

    /**
     * 🔥 主要解析接口 - 处理 token 流
     *
     * 这是架构中的核心方法，由 ViewModel 调用
     * 负责将原始 token 流转换为语义事件流
     */
    suspend fun parseTokenStream(
        messageId: String,
        tokens: kotlinx.coroutines.flow.Flow<String>,
        onEvent: suspend (SemanticEvent) -> Unit,
    ) {
        val context = ParserContext()

        try {
            Timber.tag("TB-PARSER").i("🚀 [解析启动] 开始解析 token 流: $messageId")

            tokens.collect { tokenChunk ->
                context.tokenCount++

                // 🔥 【调试】记录每个收到的token
                Timber.tag("TB-PARSER").d("📥 [收到Token] #${context.tokenCount}: '$tokenChunk'")

                // 🔥 【RAW Token采集】收集ThinkingBox端的原始token数据
                collectThinkingBoxRawTokenData(messageId, tokenChunk)

                // 记录原始 token
                if (RawTokenRecorder.isActive()) {
                    RawTokenRecorder.recordToken(tokenChunk, "StreamingThinkingMLParser")
                }

                // 记录关键标签
                if (containsKeyTags(tokenChunk)) {
                    Timber.tag("TB-PARSER").d("🏷️ [关键标签] 检测到重要标签在 token 中")
                }

                // 解析 token 块
                processTokenChunk(tokenChunk, context, messageId) { event ->
                    onEvent(event)
                }
            }

            // 流结束时发送完成事件
            Timber.tag("TB-PARSER").d("✅ [解析完成] Token 流结束，总计处理 ${context.tokenCount} 个 token")
            onEvent(SemanticEvent.StreamFinished())
        } catch (e: Exception) {
            Timber.tag("TB-PARSER").e(e, "❌ [解析错误] 流解析失败")
            onEvent(
                SemanticEvent.ParseErrorEvent(
                    com.example.gymbro.features.thinkingbox.domain.model.ParseError(
                        type = com.example.gymbro.features.thinkingbox.domain.model.ErrorType.PARSING_ERROR,
                        message = e.message ?: "Unknown parsing error",
                    ),
                ),
            )
        }
    }

    /**
     * 单 token 块解析方法（向后兼容）
     */
    suspend fun parseTokenChunk(
        tokenChunk: String,
        messageId: String,
        onEvent: suspend (SemanticEvent) -> Unit,
    ) {
        val context = ParserContext()

        try {
            processTokenChunk(tokenChunk, context, messageId, onEvent)
        } catch (e: Exception) {
            Timber.tag("TB-PARSER").e(e, "❌ [解析错误] Token 块解析失败")
            onEvent(
                SemanticEvent.ParseErrorEvent(
                    com.example.gymbro.features.thinkingbox.domain.model.ParseError(
                        type = com.example.gymbro.features.thinkingbox.domain.model.ErrorType.PARSING_ERROR,
                        message = e.message ?: "Unknown parsing error",
                    ),
                ),
            )
        }
    }

    /**
     * 核心解析逻辑 - 处理单个 token 块
     */
    private suspend fun processTokenChunk(
        tokenChunk: String,
        context: ParserContext,
        messageId: String,
        onEvent: suspend (SemanticEvent) -> Unit,
    ) {
        // 使用 XmlStreamScanner 扫描 token
        val xmlTokens = xmlScanner.feed(tokenChunk)

        xmlTokens.forEach { xmlToken ->
            // 记录 XML token 事件
            if (RawTokenRecorder.isActive()) {
                RawTokenRecorder.recordEvent("XMLToken", xmlToken.toString(), "TB-XML-SCANNER")
            }

            // 处理单个 XML token
            val semanticEvents = processXmlToken(xmlToken, context, messageId)

            // 发送所有生成的语义事件
            semanticEvents.forEach { semanticEvent ->
                if (RawTokenRecorder.isActive()) {
                    RawTokenRecorder.recordEvent("SemanticEvent", semanticEvent.toString(), "processXmlToken")
                }
                onEvent(semanticEvent)
            }
        }
    }

    /**
     * 处理单个 XML Token
     * 纯语法解析，不涉及业务逻辑
     */
    private fun processXmlToken(
        token: XmlStreamScanner.Token,
        context: ParserContext,
        messageId: String,
    ): List<SemanticEvent> {
        return when (token) {
            is XmlStreamScanner.TagOpen -> {
                handleTagOpen(token, context, messageId)
            }

            is XmlStreamScanner.TagClose -> {
                handleTagClose(token, context, messageId)
            }

            is XmlStreamScanner.Text -> {
                handleTextContent(token, context, messageId)
            }
        }
    }

    /**
     * 处理标签开启
     * 更新解析状态并生成语义事件
     */
    private fun handleTagOpen(
        token: XmlStreamScanner.TagOpen,
        context: ParserContext,
        messageId: String,
    ): List<SemanticEvent> {
        val tagName = token.name.lowercase()

        Timber.tag("TB-PARSER").v("🏷️ [标签开启] <$tagName> in state: ${context.state}")

        // 状态转换逻辑（纯解析状态，不涉及业务）
        when (tagName) {
            "thinking" -> {
                if (context.state == ParserState.PRE_THINK) {
                    context.state = ParserState.THINKING
                    Timber.tag("TB-PARSER").d("🔄 [状态转换] PRE_THINK → THINKING")
                }
            }
        }

        // 生成标签开启事件
        return listOf(SemanticEvent.TagOpened(token.name, token.attributes))
    }

    /**
     * 处理标签关闭
     * 更新解析状态并生成语义事件
     */
    private fun handleTagClose(
        token: XmlStreamScanner.TagClose,
        context: ParserContext,
        messageId: String,
    ): List<SemanticEvent> {
        val tagName = token.name.lowercase()

        Timber.tag("TB-PARSER").v("🏷️ [标签关闭] </$tagName> in state: ${context.state}")

        // 状态转换逻辑
        when (tagName) {
            "thinking" -> {
                if (context.state == ParserState.THINKING) {
                    context.state = ParserState.POST_FINAL
                    Timber.tag("TB-PARSER").d("🔄 [状态转换] THINKING → POST_FINAL")
                }
            }
        }

        // 生成标签关闭事件
        return listOf(SemanticEvent.TagClosed(token.name))
    }

    /**
     * 处理文本内容
     * 过滤空白字符并生成文本事件
     */
    private fun handleTextContent(
        token: XmlStreamScanner.Text,
        context: ParserContext,
        messageId: String,
    ): List<SemanticEvent> {
        // 过滤空白内容
        if (token.content.isBlank()) {
            return emptyList()
        }

        Timber.tag("TB-PARSER").v("📝 [文本内容] ${token.content.take(50)}... in state: ${context.state}")

        // 生成文本块事件
        return listOf(SemanticEvent.TextChunk(token.content))
    }

    /**
     * 检查 token 是否包含关键标签
     * 用于优化日志记录
     */
    private fun containsKeyTags(tokenChunk: String): Boolean {
        val keyTags =
            listOf(
                "<think>",
                "</think>",
                "<thinking>",
                "</thinking>",
                "<phase",
                "</phase>",
                "<final>",
                "</final>",
            )
        return keyTags.any { tokenChunk.contains(it, ignoreCase = true) }
    }

    /**
     * 完成解析并生成流结束事件（向后兼容）
     */
    fun finishParsing(
        messageId: String,
        onEvent: (SemanticEvent) -> Unit,
    ) {
        Timber.tag("TB-PARSER").d("✅ [手动结束] 手动触发流结束事件")
        onEvent(SemanticEvent.StreamFinished())
    }

    /**
     * 🔥 【RAW Token采集】收集ThinkingBox端的原始token数据用于调试XML标签识别问题
     *
     * 采集频率：每200个token输出一次汇总日志
     * 重点关注：包含XML标签字符的token，验证是否正确传递到ThinkingBox
     */
    private fun collectThinkingBoxRawTokenData(messageId: String, token: String) {
        synchronized(this) {
            tbRawTokenCount++
            tbRawTokenBuffer.add(token)

            // 检测XML标签字符
            if (token.contains("<") || token.contains(">")) {
                tbXmlTokenCount++
                tbXmlTokenBuffer.add(token)
            }

            // 每200个token输出一次汇总日志
            if (tbRawTokenCount % 200 == 0L) {
                val xmlTokens = tbXmlTokenBuffer.joinToString(", ") { "'$it'" }
                val recentTokens = tbRawTokenBuffer.takeLast(15).joinToString(", ") { "'$it'" }

                Timber.tag("TB-RAW-TOKENS")
                    .i("📊 [ThinkingBox RAW Token汇总] messageId=$messageId, 累计=${tbRawTokenCount}个tokens, XML标签=${tbXmlTokenCount}个")

                if (tbXmlTokenBuffer.isNotEmpty()) {
                    Timber.tag("TB-RAW-TOKENS")
                        .i("🔍 [ThinkingBox XML Tokens] $xmlTokens")
                } else {
                    Timber.tag("TB-RAW-TOKENS")
                        .w("⚠️ [ThinkingBox XML Tokens] 未检测到任何XML标签字符！")
                }

                Timber.tag("TB-RAW-TOKENS")
                    .i("📝 [ThinkingBox 最近15个Tokens] $recentTokens")

                // 清理缓冲区避免内存泄漏
                if (tbRawTokenBuffer.size > 300) {
                    tbRawTokenBuffer.clear()
                }
                if (tbXmlTokenBuffer.size > 100) {
                    tbXmlTokenBuffer.clear()
                }
            }
        }
    }
}
