package com.example.gymbro.features.workout.plan.edit

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.components.ButtonImportance
import com.example.gymbro.designSystem.components.GymBroButton
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.designSystem.theme.tokens.workoutColors
import com.example.gymbro.features.workout.plan.PlanContract
import com.example.gymbro.features.workout.plan.PlanViewModel
import com.example.gymbro.features.workout.plan.canvas.components.*
import com.example.gymbro.features.workout.plan.canvas.coordinator.UnifiedDragCoordinator
import com.example.gymbro.features.workout.plan.canvas.model.*
import com.example.gymbro.features.workout.template.TemplateContract
import com.example.gymbro.features.workout.template.TemplateViewModel

/**
 * 新版画布式训练计划编辑屏幕
 *
 * 🎯 核心特性：
 * - 统一画布设计：4周x7天网格布局
 * - 跨模块拖拽：Template和TemplateDraft的统一支持
 * - M3动画效果：流畅的拖拽和放置动画
 * - 事件驱动架构：解耦的拖拽协调系统
 * - 撤销/重做支持：完整的操作历史管理
 *
 * 🏗️ 架构升级：
 * - 画布数据模型：PlanCanvasData统一数据管理
 * - 拖拽协调器：UnifiedDragCoordinator统一控制
 * - 组件化设计：高度复用的UI组件系统
 * - 性能优化：LazyColumn虚拟化和智能重组
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PlanEditScreen(
    planId: String?,
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier,
    planViewModel: PlanViewModel = hiltViewModel(),
    templateViewModel: TemplateViewModel = hiltViewModel(),
) {
    val context = LocalContext.current

    // State监听
    val planState by planViewModel.state.collectAsStateWithLifecycle()
    val templateState by templateViewModel.state.collectAsStateWithLifecycle()

    // 拖拽协调器
    val dragCoordinator = remember { UnifiedDragCoordinator() }

    // 画布数据状态
    var canvasData by remember {
        mutableStateOf(
            PlanCanvasData.createEmpty(
                planName = if (planId != null) "编辑计划" else "新建计划",
            ),
        )
    }

    // 可拖拽数据源 - 连接真实模板数据
    val draggableTemplates = remember(templateState.templates) {
        templateState.templates.map { templateDto ->
            DraggableItemData.TemplateData.fromTemplateDto(templateDto)
        }
    }

    // 可拖拽草稿数据源 - 连接真实草稿数据
    val draggableDrafts = remember(templateState.drafts) {
        templateState.drafts.map { draft ->
            DraggableItemData.DraftData.fromDraft(draft)
        }
    }

    // 数据加载状态监听
    val isLoadingTemplates = templateState.isLoading
    val isLoadingDrafts = templateState.isLoadingDrafts
    val templateError = templateState.error

    // 初始化画布
    LaunchedEffect(planId, planState.selectedPlan) {
        planState.selectedPlan?.let { plan ->
            // 将现有计划数据转换为画布数据
            val convertedCanvasData = convertPlanToCanvas(plan, canvasData)
            canvasData = convertedCanvasData
            dragCoordinator.initializeCanvas(convertedCanvasData)
        } ?: run {
            // 新建计划模式
            dragCoordinator.initializeCanvas(canvasData)
        }
    }

    // 初始化数据加载
    LaunchedEffect(Unit) {
        if (planId != null) {
            planViewModel.selectPlan(planId)
        }
        // 加载模板和草稿数据
        templateViewModel.loadTemplates()
        templateViewModel.loadDrafts()
    }

    // 拖拽事件监听
    LaunchedEffect(dragCoordinator) {
        dragCoordinator.addDragEventListener(object : UnifiedDragCoordinator.DragEventListener {
            override fun onDrop(
                item: UnifiedDragCoordinator.DraggableItemData,
                dropZone: UnifiedDragCoordinator.DropZone,
            ) {
                // 处理拖拽成功事件
                // 可以触发触觉反馈、显示成功提示等
            }

            override fun onDragCancel(item: UnifiedDragCoordinator.DraggableItemData) {
                // 处理拖拽取消事件
            }
        })
    }

    // 🔥 简洁的初版布局设计 - 重点在功能和体验
    Scaffold(
        topBar = {
            PlanEditTopBar(
                title = canvasData.planName,
                onNavigateBack = onNavigateBack,
                onSave = {
                    // 保存画布数据到Plan
                    savePlanFromCanvas(canvasData, planViewModel, planId)
                },
                canUndo = canvasData.canUndo(),
                canRedo = canvasData.canRedo(),
                onUndo = { dragCoordinator.undo() },
                onRedo = { dragCoordinator.redo() },
                isSaving = planState.isLoading,
            )
        },
        containerColor = MaterialTheme.workoutColors.cardBackground,
        modifier = modifier,
    ) { paddingValues ->
        // 主内容区域 - 左右布局（按照用户wireframe设计）
        Row(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(Tokens.Spacing.Medium),
            horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
        ) {
            // 左侧：完整4周x7天画布（占全部宽度）
            FullPlanCanvas(
                canvasData = canvasData,
                coordinator = dragCoordinator,
                onCanvasUpdated = { updatedCanvas ->
                    canvasData = updatedCanvas
                },
                modifier = Modifier.weight(1f),
            )
            
            // 右侧：浮动拖拽源面板（自适应宽度，不影响画布）
            FloatingTemplatePanel(
                templates = draggableTemplates,
                drafts = draggableDrafts,
                coordinator = dragCoordinator,
                isLoadingTemplates = isLoadingTemplates,
                isLoadingDrafts = isLoadingDrafts,
                error = templateError,
                onRetryLoad = {
                    templateViewModel.loadTemplates()
                    templateViewModel.loadDrafts()
                },
                modifier = Modifier.weight(0.3f), // 使用weight而不是固定宽度
            )
        }
    }
}

/**
 * 完整4周x7天画布 - 按照wireframe设计
 */
@Composable
private fun FullPlanCanvas(
    canvasData: PlanCanvasData,
    coordinator: UnifiedDragCoordinator,
    onCanvasUpdated: (PlanCanvasData) -> Unit,
    modifier: Modifier = Modifier,
) {
    Surface(
        modifier = modifier,
        color = MaterialTheme.workoutColors.cardBackground,
        shape = RoundedCornerShape(Tokens.Radius.Large),
        tonalElevation = Tokens.Elevation.Small,
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(Tokens.Spacing.Medium),
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
        ) {
            // 4周网格画布
            LazyColumn(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
            ) {
                items(4) { weekIndex ->
                    val week = weekIndex + 1
                    WeekRowCanvas(
                        week = week,
                        canvasData = canvasData,
                        coordinator = coordinator,
                        onCanvasUpdated = onCanvasUpdated,
                    )
                }
            }
            
            // 底部统计信息
            CanvasStatisticsFooter(
                canvasData = canvasData,
                modifier = Modifier.fillMaxWidth(),
            )
        }
    }
}

/**
 * 单周行画布 - 7天横向排列
 */
@Composable
private fun WeekRowCanvas(
    week: Int,
    canvasData: PlanCanvasData,
    coordinator: UnifiedDragCoordinator,
    onCanvasUpdated: (PlanCanvasData) -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
    ) {
        // 周标题
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Text(
                text = "第${week}周",
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.workoutColors.textPrimary,
                fontWeight = FontWeight.Medium,
            )
            
            // 周统计
            val weekStats = calculateWeekStatistics(canvasData, week)
            if (weekStats.totalWorkouts > 0) {
                Surface(
                    color = MaterialTheme.workoutColors.accentPrimary.copy(alpha = 0.1f),
                    shape = RoundedCornerShape(Tokens.Radius.Small),
                ) {
                    Text(
                        text = "${weekStats.scheduledDays}/7天 • ${weekStats.totalWorkouts}个训练",
                        style = MaterialTheme.typography.labelMedium,
                        color = MaterialTheme.workoutColors.accentPrimary,
                        modifier = Modifier.padding(
                            horizontal = Tokens.Spacing.Small,
                            vertical = Tokens.Spacing.XSmall,
                        ),
                    )
                }
            }
        }
        
        // 7天网格
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
            modifier = Modifier.fillMaxWidth(),
        ) {
            items(7) { dayIndex ->
                val day = dayIndex + 1
                DayCanvasCell(
                    week = week,
                    day = day,
                    canvasData = canvasData,
                    coordinator = coordinator,
                    onCanvasUpdated = onCanvasUpdated,
                )
            }
        }
    }
}

/**
 * 单日画布格子
 */
@Composable
private fun DayCanvasCell(
    week: Int,
    day: Int,
    canvasData: PlanCanvasData,
    coordinator: UnifiedDragCoordinator,
    onCanvasUpdated: (PlanCanvasData) -> Unit,
    modifier: Modifier = Modifier,
) {
    val position = CanvasPosition(week = week, day = day, order = 0)
    val dayItems = canvasData.getItemsForDate(position.getDayIndex())
    val hasItems = dayItems.isNotEmpty()
    val dayName = getDayName(day)
    
    Surface(
        modifier = modifier
            .fillMaxWidth()
            .aspectRatio(0.75f), // 使用宽高比而不是固定尺寸
        color = if (hasItems) {
            MaterialTheme.workoutColors.accentPrimary.copy(alpha = 0.05f)
        } else {
            MaterialTheme.workoutColors.cardBackground
        },
        shape = RoundedCornerShape(Tokens.Radius.Medium),
        border = BorderStroke(
            width = 1.dp,
            color = if (hasItems) {
                MaterialTheme.workoutColors.accentPrimary.copy(alpha = 0.2f)
            } else {
                MaterialTheme.workoutColors.textSecondary.copy(alpha = 0.1f)
            }
        ),
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(Tokens.Spacing.Small),
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.XSmall),
        ) {
            // 日期标题
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Text(
                    text = dayName,
                    style = MaterialTheme.typography.labelMedium,
                    color = MaterialTheme.workoutColors.textSecondary,
                    fontWeight = FontWeight.Medium,
                )
                
                if (hasItems) {
                    Surface(
                        color = MaterialTheme.workoutColors.accentPrimary,
                        shape = RoundedCornerShape(Tokens.Radius.Small),
                        modifier = Modifier.size(Tokens.Icon.Small),
                    ) {
                        Box(
                            contentAlignment = Alignment.Center,
                            modifier = Modifier.fillMaxSize(),
                        ) {
                            Text(
                                text = "${dayItems.size}",
                                style = MaterialTheme.typography.labelSmall,
                                color = MaterialTheme.workoutColors.textPrimary,
                                fontWeight = FontWeight.Bold,
                            )
                        }
                    }
                }
            }
            
            // 训练项目列表或空状态
            if (hasItems) {
                LazyColumn(
                    modifier = Modifier.weight(1f),
                    verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.XSmall),
                ) {
                    items(dayItems.take(3)) { item -> // 最多显示3个
                        DayTrainingChip(
                            item = item,
                            onRemove = {
                                // TODO: 实现移除逻辑
                            }
                        )
                    }
                    
                    if (dayItems.size > 3) {
                        item {
                            Text(
                                text = "+${dayItems.size - 3}更多",
                                style = MaterialTheme.typography.labelSmall,
                                color = MaterialTheme.workoutColors.textSecondary,
                                modifier = Modifier.padding(vertical = Tokens.Spacing.XSmall),
                            )
                        }
                    }
                }
            } else {
                // 空状态拖拽提示
                Box(
                    modifier = Modifier
                        .weight(1f)
                        .fillMaxWidth(),
                    contentAlignment = Alignment.Center,
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.XSmall),
                    ) {
                        Icon(
                            imageVector = Icons.Default.Add,
                            contentDescription = "添加训练",
                            tint = MaterialTheme.workoutColors.textSecondary.copy(alpha = 0.4f),
                            modifier = Modifier.size(Tokens.Icon.Medium),
                        )
                        Text(
                            text = "拖拽至此",
                            style = MaterialTheme.typography.labelSmall,
                            color = MaterialTheme.workoutColors.textSecondary.copy(alpha = 0.6f),
                            textAlign = TextAlign.Center,
                        )
                    }
                }
            }
        }
    }
}

/**
 * 日期训练芯片 - 简化版
 */
@Composable
private fun DayTrainingChip(
    item: CanvasItem,
    onRemove: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Surface(
        modifier = modifier.fillMaxWidth(),
        color = when (item) {
            is CanvasItem.TemplateItem -> MaterialTheme.workoutColors.accentPrimary.copy(alpha = 0.1f)
            is CanvasItem.DraftItem -> MaterialTheme.workoutColors.accentSecondary.copy(alpha = 0.1f)
            is CanvasItem.CustomItem -> MaterialTheme.workoutColors.textSecondary.copy(alpha = 0.1f)
        },
        shape = RoundedCornerShape(Tokens.Radius.Small),
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    horizontal = Tokens.Spacing.XSmall,
                    vertical = 2.dp, // Using direct dp value instead of XXSmall
                ),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Text(
                text = item.name.take(6) + if (item.name.length > 6) "..." else "",
                style = MaterialTheme.typography.labelSmall,
                color = when (item) {
                    is CanvasItem.TemplateItem -> MaterialTheme.workoutColors.accentPrimary
                    is CanvasItem.DraftItem -> MaterialTheme.workoutColors.accentSecondary
                    is CanvasItem.CustomItem -> MaterialTheme.workoutColors.textSecondary
                },
                fontWeight = FontWeight.Medium,
                maxLines = 1,
            )
            
            Icon(
                imageVector = Icons.Default.Close,
                contentDescription = "移除",
                tint = MaterialTheme.workoutColors.textSecondary.copy(alpha = 0.6f),
                modifier = Modifier
                    .size(Tokens.Icon.XSmall)
                    .clickable { onRemove() },
            )
        }
    }
}

/**
 * 画布统计信息底栏
 */
@Composable
private fun CanvasStatisticsFooter(
    canvasData: PlanCanvasData,
    modifier: Modifier = Modifier,
) {
    val totalStats = calculateTotalStatistics(canvasData)
    
    Surface(
        modifier = modifier,
        color = MaterialTheme.workoutColors.cardBackground,
        shape = RoundedCornerShape(Tokens.Radius.Medium),
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Tokens.Spacing.Medium),
            horizontalArrangement = Arrangement.SpaceEvenly,
            verticalAlignment = Alignment.CenterVertically,
        ) {
            StatisticItem(
                label = "总重量",
                value = 0, // TODO: 计算总重量
                total = null,
                unit = "kg",
                color = MaterialTheme.workoutColors.accentPrimary,
            )
            
            StatisticItem(
                label = "总训练天数",
                value = totalStats.scheduledDays,
                total = totalStats.totalDays,
                unit = "天",
                color = MaterialTheme.workoutColors.accentSecondary,
            )
            
            StatisticItem(
                label = "已经完成",
                value = 0,
                total = totalStats.scheduledDays,
                unit = "",
                color = MaterialTheme.workoutColors.successPrimary,
            )
        }
    }
}

/**
 * 浮动模板面板 - 右侧拖拽源，使用tokens系统
 */
@Composable
private fun FloatingTemplatePanel(
    templates: List<DraggableItemData.TemplateData>,
    drafts: List<DraggableItemData.DraftData>,
    coordinator: UnifiedDragCoordinator,
    isLoadingTemplates: Boolean = false,
    isLoadingDrafts: Boolean = false,
    error: UiText? = null,
    onRetryLoad: () -> Unit = {},
    modifier: Modifier = Modifier,
) {
    var showingTemplates by remember { mutableStateOf(true) }
    val currentList = if (showingTemplates) templates else drafts
    val isCurrentLoading = if (showingTemplates) isLoadingTemplates else isLoadingDrafts
    
    Surface(
        modifier = modifier.fillMaxHeight(),
        color = MaterialTheme.workoutColors.cardBackground,
        shape = RoundedCornerShape(Tokens.Radius.Large),
        tonalElevation = Tokens.Elevation.Medium,
        shadowElevation = Tokens.Elevation.Small,
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(Tokens.Spacing.Medium),
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
        ) {
            // 切换按钮 - 按照wireframe中的"草稿/模板"样式
            ToggleButton(
                showingTemplates = showingTemplates,
                onToggle = { showingTemplates = !showingTemplates },
                templatesCount = templates.size,
                draftsCount = drafts.size,
                isLoading = isCurrentLoading,
            )

            // 内容区域：加载状态、错误状态或列表
            when {
                // 错误状态
                error != null -> {
                    ErrorStateContent(
                        onRetry = onRetryLoad,
                        modifier = Modifier.weight(1f),
                    )
                }
                
                // 加载状态
                isCurrentLoading -> {
                    LoadingStateContent(
                        modifier = Modifier.weight(1f),
                    )
                }
                
                // 空列表状态
                currentList.isEmpty() -> {
                    EmptyStateContent(
                        showingTemplates = showingTemplates,
                        modifier = Modifier.weight(1f),
                    )
                }
                
                // 数据列表
                else -> {
                    LazyColumn(
                        modifier = Modifier.weight(1f),
                        verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
                        contentPadding = PaddingValues(vertical = Tokens.Spacing.Small),
                    ) {
                        items(currentList.size) { index ->
                            UnifiedDraggableItem(
                                itemData = currentList[index],
                                coordinator = coordinator,
                                modifier = Modifier.fillMaxWidth(),
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * 切换按钮组件 - 完全使用tokens
 */
@Composable
private fun ToggleButton(
    showingTemplates: Boolean,
    onToggle: () -> Unit,
    templatesCount: Int,
    draftsCount: Int,
    isLoading: Boolean,
    modifier: Modifier = Modifier,
) {
    Surface(
        onClick = onToggle,
        modifier = modifier.fillMaxWidth(),
        color = if (showingTemplates) {
            MaterialTheme.workoutColors.accentPrimary.copy(alpha = 0.2f)
        } else {
            MaterialTheme.workoutColors.accentSecondary.copy(alpha = 0.2f)
        },
        shape = RoundedCornerShape(Tokens.Radius.Medium),
        border = BorderStroke(
            width = 1.dp,
            color = if (showingTemplates) {
                MaterialTheme.workoutColors.accentPrimary.copy(alpha = 0.8f)
            } else {
                MaterialTheme.workoutColors.accentSecondary.copy(alpha = 0.8f)
            }
        ),
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    horizontal = Tokens.Spacing.Medium,
                    vertical = Tokens.Spacing.Small,
                ),
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Icon(
                imageVector = if (showingTemplates) Icons.Default.FitnessCenter else Icons.Default.Edit,
                contentDescription = null,
                tint = if (showingTemplates) {
                    MaterialTheme.workoutColors.accentPrimary
                } else {
                    MaterialTheme.workoutColors.accentSecondary
                },
                modifier = Modifier.size(Tokens.Icon.Medium),
            )
            
            Spacer(modifier = Modifier.width(Tokens.Spacing.Small))
            
            Text(
                text = if (showingTemplates) "模板" else "草稿",
                style = MaterialTheme.typography.titleMedium,
                color = if (showingTemplates) {
                    MaterialTheme.workoutColors.accentPrimary
                } else {
                    MaterialTheme.workoutColors.accentSecondary
                },
                fontWeight = FontWeight.SemiBold,
            )
            
            if (!isLoading) {
                Spacer(modifier = Modifier.width(Tokens.Spacing.XSmall))
                
                // 数据计数显示
                Surface(
                    color = MaterialTheme.workoutColors.cardBackground,
                    shape = RoundedCornerShape(Tokens.Radius.Small),
                ) {
                    Text(
                        text = if (showingTemplates) "$templatesCount" else "$draftsCount",
                        style = MaterialTheme.typography.labelMedium,
                        color = MaterialTheme.workoutColors.textSecondary,
                        modifier = Modifier.padding(
                            horizontal = Tokens.Spacing.Small,
                            vertical = Tokens.Spacing.XSmall,
                        ),
                    )
                }
            }
        }
    }
}

/**
 * 加载状态内容
 */
@Composable
private fun LoadingStateContent(
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center,
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
        ) {
            CircularProgressIndicator(
                modifier = Modifier.size(Tokens.Icon.Large),
                color = MaterialTheme.workoutColors.accentPrimary,
                strokeWidth = 3.dp,
            )
            Text(
                text = "加载中...",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.workoutColors.textSecondary,
            )
        }
    }
}

/**
 * 错误状态内容
 */
@Composable
private fun ErrorStateContent(
    onRetry: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center,
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
        ) {
            Icon(
                imageVector = Icons.Default.Warning,
                contentDescription = null,
                tint = MaterialTheme.workoutColors.errorPrimary,
                modifier = Modifier.size(Tokens.Icon.Large),
            )
            
            Text(
                text = "加载失败",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.workoutColors.textSecondary,
                textAlign = TextAlign.Center,
            )
            
            Button(
                onClick = onRetry,
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.workoutColors.accentPrimary,
                    contentColor = MaterialTheme.workoutColors.textPrimary,
                ),
                shape = RoundedCornerShape(Tokens.Radius.Medium),
            ) {
                Text(text = "重试")
            }
        }
    }
}

/**
 * 空状态内容
 */
@Composable
private fun EmptyStateContent(
    showingTemplates: Boolean,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center,
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
        ) {
            Icon(
                imageVector = if (showingTemplates) Icons.Default.FitnessCenter else Icons.Default.Edit,
                contentDescription = null,
                tint = MaterialTheme.workoutColors.textSecondary.copy(alpha = 0.6f),
                modifier = Modifier.size(Tokens.Icon.Large),
            )
            Text(
                text = if (showingTemplates) "暂无模板" else "暂无草稿",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.workoutColors.textSecondary,
                textAlign = TextAlign.Center,
            )
            Text(
                text = "点击上方按钮切换",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.workoutColors.textSecondary.copy(alpha = 0.8f),
                textAlign = TextAlign.Center,
            )
        }
    }
}

/**
 * 计划统计信息栏 - 显示训练完成情况
 */
@Composable
private fun PlanStatisticsBar(
    canvasData: PlanCanvasData,
    selectedWeek: Int,
    modifier: Modifier = Modifier,
) {
    // 计算统计数据
    val weekStats = calculateWeekStatistics(canvasData, selectedWeek)
    val totalStats = calculateTotalStatistics(canvasData)
    
    Surface(
        modifier = modifier,
        color = MaterialTheme.workoutColors.cardBackground,
        shape = RoundedCornerShape(Tokens.Radius.Medium),
        tonalElevation = Tokens.Elevation.Small,
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Tokens.Spacing.Medium),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically,
        ) {
            // 左侧：当前周统计
            StatisticItem(
                label = "本周",
                value = weekStats.scheduledDays,
                total = 7,
                unit = "天",
                color = MaterialTheme.workoutColors.accentPrimary,
            )
            
            Spacer(modifier = Modifier.width(Tokens.Spacing.Medium))
            
            // 中间：总训练数量
            StatisticItem(
                label = "总训练",
                value = weekStats.totalWorkouts,
                total = null,
                unit = "个",
                color = MaterialTheme.workoutColors.accentSecondary,
            )
            
            Spacer(modifier = Modifier.width(Tokens.Spacing.Medium))
            
            // 右侧：整体完成度
            StatisticItem(
                label = "整体进度",
                value = totalStats.scheduledDays,
                total = totalStats.totalDays,
                unit = "天",
                color = MaterialTheme.workoutColors.successPrimary,
                showProgress = true,
            )
        }
    }
}

/**
 * 统计项组件
 */
@Composable
private fun StatisticItem(
    label: String,
    value: Int,
    total: Int?,
    unit: String,
    color: androidx.compose.ui.graphics.Color,
    showProgress: Boolean = false,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.XSmall),
    ) {
        // 标签
        Text(
            text = label,
            style = MaterialTheme.typography.labelMedium,
            color = MaterialTheme.workoutColors.textSecondary,
        )
        
        // 数值和进度
        Row(
            verticalAlignment = Alignment.Bottom,
            horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.XSmall),
        ) {
            Text(
                text = "$value",
                style = MaterialTheme.typography.titleLarge,
                color = color,
                fontWeight = FontWeight.Bold,
            )
            
            if (total != null) {
                Text(
                    text = "/$total",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.workoutColors.textSecondary,
                )
            }
            
            Text(
                text = unit,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.workoutColors.textSecondary,
            )
        }
        
        // 进度条（可选）
        if (showProgress && total != null && total > 0) {
            val progress = value.toFloat() / total.toFloat()
            
            LinearProgressIndicator(
                progress = progress,
                modifier = Modifier
                    .width(60.dp)
                    .height(4.dp),
                color = color,
                trackColor = color.copy(alpha = 0.2f),
            )
        }
    }
}

/**
 * 周统计数据类
 */
private data class WeekStatistics(
    val scheduledDays: Int,
    val totalWorkouts: Int,
    val estimatedDuration: Int,
)

/**
 * 总统计数据类
 */
private data class TotalStatistics(
    val scheduledDays: Int,
    val totalDays: Int,
    val totalWorkouts: Int,
    val totalWeeks: Int,
)

/**
 * 计算周统计数据
 */
private fun calculateWeekStatistics(canvasData: PlanCanvasData, week: Int): WeekStatistics {
    var scheduledDays = 0
    var totalWorkouts = 0
    var estimatedDuration = 0
    
    // 遍历一周的7天
    for (day in 1..7) {
        val dayIndex = (week - 1) * 7 + day
        val dayItems = canvasData.getItemsForDate(dayIndex)
        
        if (dayItems.isNotEmpty()) {
            scheduledDays++
            totalWorkouts += dayItems.size
            
            // 计算预估时长
            dayItems.forEach { item ->
                when (item) {
                    is CanvasItem.TemplateItem -> {
                        item.estimatedDuration?.let { estimatedDuration += it }
                    }
                    is CanvasItem.DraftItem -> {
                        item.estimatedDuration?.let { estimatedDuration += it }
                    }
                    is CanvasItem.CustomItem -> {
                        item.estimatedDuration?.let { estimatedDuration += it }
                    }
                }
            }
        }
    }
    
    return WeekStatistics(
        scheduledDays = scheduledDays,
        totalWorkouts = totalWorkouts,
        estimatedDuration = estimatedDuration,
    )
}

/**
 * 计算总统计数据
 */
private fun calculateTotalStatistics(canvasData: PlanCanvasData): TotalStatistics {
    val config = canvasData.canvasConfig
    var scheduledDays = 0
    var totalWorkouts = 0
    
    // 遍历所有已安排的日期
    canvasData.scheduleItems.forEach { (_, items) ->
        if (items.isNotEmpty()) {
            scheduledDays++
            totalWorkouts += items.size
        }
    }
    
    return TotalStatistics(
        scheduledDays = scheduledDays,
        totalDays = config.weekCount * config.daysPerWeek,
        totalWorkouts = totalWorkouts,
        totalWeeks = config.weekCount,
    )
}

/**
 * 周次选择Tab - 按照用户示意图设计
 */
@Composable
private fun WeekSelectorTabs(
    selectedWeek: Int,
    onWeekSelected: (Int) -> Unit,
    modifier: Modifier = Modifier,
) {
    Surface(
        modifier = modifier,
        color = MaterialTheme.workoutColors.cardBackground,
        shape = RoundedCornerShape(Tokens.Radius.Large),
        tonalElevation = Tokens.Elevation.Small,
    ) {
        TabRow(
            selectedTabIndex = selectedWeek - 1,
            containerColor = MaterialTheme.workoutColors.cardBackground,
            contentColor = MaterialTheme.workoutColors.accentPrimary,
            divider = {},
            indicator = { tabPositions ->
                if (tabPositions.isNotEmpty() && selectedWeek in 1..4) {
                    TabRowDefaults.SecondaryIndicator(
                        modifier = Modifier,
                        color = MaterialTheme.workoutColors.accentPrimary,
                        height = 3.dp,
                    )
                }
            },
        ) {
            repeat(4) { index ->
                val week = index + 1
                val isSelected = selectedWeek == week
                
                Tab(
                    selected = isSelected,
                    onClick = { onWeekSelected(week) },
                    selectedContentColor = MaterialTheme.workoutColors.accentPrimary,
                    unselectedContentColor = MaterialTheme.workoutColors.textSecondary,
                ) {
                    Text(
                        text = "第${week}周",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = if (isSelected) FontWeight.SemiBold else FontWeight.Normal,
                        modifier = Modifier.padding(vertical = Tokens.Spacing.Medium),
                    )
                }
            }
        }
    }
}

/**
 * 单周画布 - 周一到周日垂直排列（按照用户示意图）
 */
@Composable
private fun WeeklyPlanCanvas(
    week: Int,
    canvasData: PlanCanvasData,
    coordinator: UnifiedDragCoordinator,
    onCanvasUpdated: (PlanCanvasData) -> Unit,
    modifier: Modifier = Modifier,
) {
    Surface(
        modifier = modifier,
        color = MaterialTheme.workoutColors.cardBackground,
        shape = RoundedCornerShape(Tokens.Radius.Large),
        tonalElevation = Tokens.Elevation.Small,
        shadowElevation = Tokens.Elevation.Small,
    ) {
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(Tokens.Spacing.Medium),
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
        ) {
            // 周一到周日，每天一行
            items(7) { dayIndex ->
                val day = dayIndex + 1
                val dayName = getDayName(day)
                
                DayPlanRow(
                    week = week,
                    day = day,
                    dayName = dayName,
                    canvasData = canvasData,
                    coordinator = coordinator,
                    onCanvasUpdated = onCanvasUpdated,
                )
            }
        }
    }
}

/**
 * 统一画布顶部栏 - 遵循Template Edit Screen设计规范
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun PlanEditTopBar(
    title: String,
    onNavigateBack: () -> Unit,
    onSave: () -> Unit,
    canUndo: Boolean,
    canRedo: Boolean,
    onUndo: () -> Unit,
    onRedo: () -> Unit,
    isSaving: Boolean = false,
    modifier: Modifier = Modifier,
) {
    TopAppBar(
        title = {
            Text(
                text = title,
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.workoutColors.accentPrimary,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
            )
        },
        navigationIcon = {
            IconButton(onClick = onNavigateBack) {
                Icon(
                    imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                    contentDescription = "返回",
                    tint = MaterialTheme.workoutColors.accentPrimary,
                )
            }
        },
        actions = {
            // 撤销按钮
            IconButton(
                onClick = onUndo,
                enabled = canUndo && !isSaving,
            ) {
                Icon(
                    imageVector = Icons.Default.Undo,
                    contentDescription = "撤销",
                    tint = if (canUndo && !isSaving) {
                        MaterialTheme.workoutColors.accentPrimary
                    } else {
                        MaterialTheme.workoutColors.textSecondary.copy(alpha = 0.5f)
                    },
                )
            }

            // 重做按钮
            IconButton(
                onClick = onRedo,
                enabled = canRedo && !isSaving,
            ) {
                Icon(
                    imageVector = Icons.Default.Redo,
                    contentDescription = "重做",
                    tint = if (canRedo && !isSaving) {
                        MaterialTheme.workoutColors.accentPrimary
                    } else {
                        MaterialTheme.workoutColors.textSecondary.copy(alpha = 0.5f)
                    },
                )
            }

            // 保存按钮
            Button(
                onClick = onSave,
                enabled = !isSaving,
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.workoutColors.accentPrimary,
                    contentColor = MaterialTheme.workoutColors.textPrimary,
                ),
                modifier = Modifier.padding(end = Tokens.Spacing.Small),
            ) {
                if (isSaving) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        strokeWidth = 2.dp,
                        color = MaterialTheme.workoutColors.textPrimary,
                    )
                } else {
                    Text(text = "保存")
                }
            }
        },
        colors = TopAppBarDefaults.topAppBarColors(
            containerColor = MaterialTheme.workoutColors.cardBackground,
            titleContentColor = MaterialTheme.workoutColors.accentPrimary,
            navigationIconContentColor = MaterialTheme.workoutColors.accentPrimary,
            actionIconContentColor = MaterialTheme.workoutColors.accentPrimary,
        ),
        modifier = modifier,
    )
}

/**
 * 日期计划行组件 - 每天的训练安排（按照用户示意图）
 */
@Composable
private fun DayPlanRow(
    week: Int,
    day: Int,
    dayName: String,
    canvasData: PlanCanvasData,
    coordinator: UnifiedDragCoordinator,
    onCanvasUpdated: (PlanCanvasData) -> Unit,
    modifier: Modifier = Modifier,
) {
    val position = CanvasPosition(week = week, day = day, order = 0)
    val dayItems = canvasData.getItemsForDate(position.getDayIndex())
    val hasItems = dayItems.isNotEmpty()
    
    Surface(
        modifier = modifier.fillMaxWidth(),
        color = if (hasItems) {
            MaterialTheme.workoutColors.accentPrimary.copy(alpha = 0.1f)
        } else {
            MaterialTheme.workoutColors.cardBackground
        },
        shape = RoundedCornerShape(Tokens.Radius.Medium),
        border = BorderStroke(
            width = 1.dp,
            color = if (hasItems) {
                MaterialTheme.workoutColors.accentPrimary.copy(alpha = 0.3f)
            } else {
                MaterialTheme.workoutColors.textSecondary.copy(alpha = 0.2f)
            }
        ),
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Tokens.Spacing.Medium),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically,
        ) {
            // 左侧：日期信息
            Column(
                modifier = Modifier.width(60.dp),
                horizontalAlignment = Alignment.Start,
            ) {
                Text(
                    text = dayName,
                    style = MaterialTheme.typography.titleSmall,
                    color = MaterialTheme.workoutColors.textPrimary,
                    fontWeight = FontWeight.Medium,
                )
                Text(
                    text = getDateForWeekDay(week, day),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.workoutColors.textSecondary,
                )
            }
            
            Spacer(modifier = Modifier.width(Tokens.Spacing.Medium))
            
            // 中间：训练安排显示区域
            if (hasItems) {
                // 显示已安排的训练
                LazyRow(
                    modifier = Modifier.weight(1f),
                    horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
                ) {
                    items(dayItems) { item ->
                        DayPlanItemChip(
                            item = item,
                            onRemove = {
                                // TODO: 实现移除逻辑，更新画布数据
                            }
                        )
                    }
                }
            } else {
                // 空状态拖拽提示区域
                Box(
                    modifier = Modifier
                        .weight(1f)
                        .height(40.dp),
                    contentAlignment = Alignment.Center,
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
                    ) {
                        Icon(
                            imageVector = Icons.Default.Add,
                            contentDescription = "添加训练",
                            tint = MaterialTheme.workoutColors.textSecondary.copy(alpha = 0.6f),
                            modifier = Modifier.size(16.dp),
                        )
                        Text(
                            text = "拖拽模板到此处",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.workoutColors.textSecondary.copy(alpha = 0.6f),
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.width(Tokens.Spacing.Medium))
            
            // 右侧：训练统计和快速添加
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
            ) {
                if (hasItems) {
                    // 显示训练数量
                    Surface(
                        color = MaterialTheme.workoutColors.accentPrimary.copy(alpha = 0.1f),
                        shape = RoundedCornerShape(Tokens.Radius.Small),
                    ) {
                        Text(
                            text = "${dayItems.size}",
                            style = MaterialTheme.typography.labelMedium,
                            color = MaterialTheme.workoutColors.accentPrimary,
                            fontWeight = FontWeight.Medium,
                            modifier = Modifier.padding(
                                horizontal = Tokens.Spacing.Small,
                                vertical = Tokens.Spacing.XSmall,
                            ),
                        )
                    }
                }
                
                // 快速添加按钮
                IconButton(
                    onClick = {
                        // TODO: 实现快速添加模板功能
                    },
                    modifier = Modifier.size(32.dp),
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = "快速添加",
                        tint = MaterialTheme.workoutColors.textSecondary,
                        modifier = Modifier.size(16.dp),
                    )
                }
            }
        }
    }
}

/**
 * 日期计划项芯片组件
 */
@Composable
private fun DayPlanItemChip(
    item: CanvasItem,
    onRemove: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Surface(
        modifier = modifier,
        color = when (item) {
            is CanvasItem.TemplateItem -> MaterialTheme.workoutColors.accentPrimary.copy(alpha = 0.15f)
            is CanvasItem.DraftItem -> MaterialTheme.workoutColors.accentSecondary.copy(alpha = 0.15f)
            is CanvasItem.CustomItem -> MaterialTheme.workoutColors.textSecondary.copy(alpha = 0.15f)
        },
        shape = RoundedCornerShape(Tokens.Radius.Small),
        border = BorderStroke(
            width = 1.dp,
            color = when (item) {
                is CanvasItem.TemplateItem -> MaterialTheme.workoutColors.accentPrimary.copy(alpha = 0.3f)
                is CanvasItem.DraftItem -> MaterialTheme.workoutColors.accentSecondary.copy(alpha = 0.3f)
                is CanvasItem.CustomItem -> MaterialTheme.workoutColors.textSecondary.copy(alpha = 0.3f)
            }
        ),
    ) {
        Row(
            modifier = Modifier.padding(
                horizontal = Tokens.Spacing.Small,
                vertical = Tokens.Spacing.XSmall,
            ),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.XSmall),
        ) {
            // 类型图标
            Icon(
                imageVector = when (item) {
                    is CanvasItem.TemplateItem -> Icons.Default.FitnessCenter
                    is CanvasItem.DraftItem -> Icons.Default.Edit
                    is CanvasItem.CustomItem -> Icons.Default.FolderOpen
                },
                contentDescription = null,
                tint = when (item) {
                    is CanvasItem.TemplateItem -> MaterialTheme.workoutColors.accentPrimary
                    is CanvasItem.DraftItem -> MaterialTheme.workoutColors.accentSecondary
                    is CanvasItem.CustomItem -> MaterialTheme.workoutColors.textSecondary
                },
                modifier = Modifier.size(14.dp),
            )
            
            // 名称
            Text(
                text = item.name,
                style = MaterialTheme.typography.labelMedium,
                color = MaterialTheme.workoutColors.textPrimary,
                fontWeight = FontWeight.Medium,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier.widthIn(max = 80.dp),
            )
            
            // 移除按钮
            Icon(
                imageVector = Icons.Default.Close,
                contentDescription = "移除",
                tint = MaterialTheme.workoutColors.textSecondary,
                modifier = Modifier
                    .size(14.dp)
                    .clickable { onRemove() },
            )
        }
    }
}

/**
 * 集成的右侧模板源面板 - 一体化设计
 */
@Composable
private fun IntegratedTemplatePanel(
    templates: List<DraggableItemData.TemplateData>,
    drafts: List<DraggableItemData.DraftData>,
    coordinator: UnifiedDragCoordinator,
    modifier: Modifier = Modifier,
) {
    var showingTemplates by remember { mutableStateOf(true) }
    val currentList = if (showingTemplates) templates else drafts
    
    Surface(
        modifier = modifier.fillMaxHeight(),
        color = MaterialTheme.workoutColors.cardBackground,
        shape = RoundedCornerShape(Tokens.Radius.Large),
        tonalElevation = Tokens.Elevation.Small,
        shadowElevation = Tokens.Elevation.Small,
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(Tokens.Spacing.Medium),
        ) {
            // 标题和切换按钮一体化
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Text(
                    text = "模板源",
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.workoutColors.textPrimary,
                    fontWeight = FontWeight.SemiBold,
                )
                
                // 切换按钮（显示当前状态）
                Surface(
                    onClick = { showingTemplates = !showingTemplates },
                    color = if (showingTemplates) {
                        MaterialTheme.workoutColors.accentPrimary.copy(alpha = 0.1f)
                    } else {
                        MaterialTheme.workoutColors.accentSecondary.copy(alpha = 0.1f)
                    },
                    shape = RoundedCornerShape(Tokens.Radius.Medium),
                ) {
                    Row(
                        modifier = Modifier.padding(
                            horizontal = Tokens.Spacing.Medium,
                            vertical = Tokens.Spacing.Small,
                        ),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
                    ) {
                        Icon(
                            imageVector = if (showingTemplates) Icons.Default.FitnessCenter else Icons.Default.Edit,
                            contentDescription = null,
                            tint = if (showingTemplates) {
                                MaterialTheme.workoutColors.accentPrimary
                            } else {
                                MaterialTheme.workoutColors.accentSecondary
                            },
                            modifier = Modifier.size(16.dp),
                        )
                        Text(
                            text = if (showingTemplates) "模板" else "草稿",
                            style = MaterialTheme.typography.labelMedium,
                            color = if (showingTemplates) {
                                MaterialTheme.workoutColors.accentPrimary
                            } else {
                                MaterialTheme.workoutColors.accentSecondary
                            },
                        )
                        Text(
                            text = "(${currentList.size})",
                            style = MaterialTheme.typography.labelSmall,
                            color = MaterialTheme.workoutColors.textSecondary,
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(Tokens.Spacing.Medium))

            // 模板列表
            if (currentList.isEmpty()) {
                // 空状态
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .weight(1f),
                    contentAlignment = Alignment.Center,
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
                    ) {
                        Icon(
                            imageVector = if (showingTemplates) Icons.Default.FitnessCenter else Icons.Default.Edit,
                            contentDescription = null,
                            tint = MaterialTheme.workoutColors.textSecondary.copy(alpha = 0.6f),
                            modifier = Modifier.size(32.dp),
                        )
                        Text(
                            text = if (showingTemplates) "暂无可用模板" else "暂无草稿",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.workoutColors.textSecondary,
                            textAlign = TextAlign.Center,
                        )
                        Text(
                            text = "点击上方按钮切换",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.workoutColors.textSecondary.copy(alpha = 0.7f),
                            textAlign = TextAlign.Center,
                        )
                    }
                }
            } else {
                // 模板列表
                LazyColumn(
                    modifier = Modifier.weight(1f),
                    verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
                ) {
                    items(currentList.size) { index ->
                        UnifiedDraggableItem(
                            itemData = currentList[index],
                            coordinator = coordinator,
                            modifier = Modifier.fillMaxWidth(),
                        )
                    }
                }
            }
        }
    }
}

/**
 * 紧凑模板项 - 用于浮动面板
 */
@Composable
private fun CompactTemplateItem(
    itemData: DraggableItemData,
    coordinator: UnifiedDragCoordinator,
    modifier: Modifier = Modifier,
) {
    Surface(
        modifier = modifier
            .size(60.dp)
            .clip(RoundedCornerShape(Tokens.Radius.Medium)),
        color = when (itemData) {
            is DraggableItemData.TemplateData -> MaterialTheme.workoutColors.accentPrimary.copy(alpha = 0.2f)
            is DraggableItemData.DraftData -> MaterialTheme.workoutColors.accentSecondary.copy(alpha = 0.2f)
            else -> MaterialTheme.workoutColors.textSecondary.copy(alpha = 0.2f)
        },
        onClick = {
            // 点击快速添加到画布
            // TODO: 实现快速添加逻辑
        },
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(Tokens.Spacing.XSmall),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center,
        ) {
            Icon(
                imageVector = when (itemData) {
                    is DraggableItemData.TemplateData -> Icons.Default.FitnessCenter
                    is DraggableItemData.DraftData -> Icons.Default.Edit
                    else -> Icons.Default.FolderOpen
                },
                contentDescription = null,
                tint = when (itemData) {
                    is DraggableItemData.TemplateData -> MaterialTheme.workoutColors.accentPrimary
                    is DraggableItemData.DraftData -> MaterialTheme.workoutColors.accentSecondary
                    else -> MaterialTheme.workoutColors.textPrimary
                },
                modifier = Modifier.size(24.dp),
            )
            
            Spacer(modifier = Modifier.height(2.dp))
            
            Text(
                text = itemData.displayName.take(4), // 只显示前4个字符
                style = MaterialTheme.typography.labelSmall,
                color = when (itemData) {
                    is DraggableItemData.TemplateData -> MaterialTheme.workoutColors.accentPrimary
                    is DraggableItemData.DraftData -> MaterialTheme.workoutColors.accentSecondary
                    else -> MaterialTheme.workoutColors.textPrimary
                },
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                textAlign = TextAlign.Center,
            )
        }
    }
}

/**
 * 展开的模板库 - 全屏浮层
 */
@Composable
private fun ExpandedTemplateLibrary(
    templates: List<DraggableItemData.TemplateData>,
    drafts: List<DraggableItemData.DraftData>,
    coordinator: UnifiedDragCoordinator,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier,
) {
    // 半透明背景
    Surface(
        modifier = modifier,
        color = MaterialTheme.workoutColors.cardBackground.copy(alpha = 0.6f),
        onClick = onDismiss,
    ) {
        // 模板库面板（居中）
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center,
        ) {
            Surface(
                modifier = Modifier
                    .fillMaxWidth(0.85f)
                    .fillMaxHeight(0.8f),
                color = MaterialTheme.workoutColors.cardBackground,
                shape = RoundedCornerShape(Tokens.Radius.XLarge),
                tonalElevation = Tokens.Elevation.Large,
                shadowElevation = Tokens.Elevation.Large,
            ) {
                Column(
                    modifier = Modifier.fillMaxSize(),
                ) {
                    // 标题栏
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(Tokens.Spacing.Medium),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        Text(
                            text = "模板库",
                            style = MaterialTheme.typography.headlineSmall,
                            color = MaterialTheme.workoutColors.textPrimary,
                            fontWeight = FontWeight.SemiBold,
                        )
                        
                        IconButton(onClick = onDismiss) {
                            Icon(
                                imageVector = Icons.Default.Close,
                                contentDescription = "关闭",
                                tint = MaterialTheme.workoutColors.textSecondary,
                            )
                        }
                    }

                    HorizontalDivider(
                        color = MaterialTheme.workoutColors.textSecondary.copy(alpha = 0.2f)
                    )

                    // Tab + 内容
                    var selectedTab by remember { mutableIntStateOf(0) }
                    
                    TabRow(
                        selectedTabIndex = selectedTab,
                        containerColor = MaterialTheme.workoutColors.cardBackground,
                        contentColor = MaterialTheme.workoutColors.accentPrimary,
                        modifier = Modifier.padding(horizontal = Tokens.Spacing.Medium),
                    ) {
                        Tab(
                            selected = selectedTab == 0,
                            onClick = { selectedTab = 0 },
                            selectedContentColor = MaterialTheme.workoutColors.accentPrimary,
                            unselectedContentColor = MaterialTheme.workoutColors.textSecondary,
                        ) {
                            Text(
                                text = "模板 (${templates.size})",
                                modifier = Modifier.padding(vertical = Tokens.Spacing.Medium),
                                style = MaterialTheme.typography.titleMedium,
                            )
                        }

                        Tab(
                            selected = selectedTab == 1,
                            onClick = { selectedTab = 1 },
                            selectedContentColor = MaterialTheme.workoutColors.accentPrimary,
                            unselectedContentColor = MaterialTheme.workoutColors.textSecondary,
                        ) {
                            Text(
                                text = "草稿 (${drafts.size})",
                                modifier = Modifier.padding(vertical = Tokens.Spacing.Medium),
                                style = MaterialTheme.typography.titleMedium,
                            )
                        }
                    }

                    // 模板网格
                    LazyVerticalGrid(
                        columns = GridCells.Adaptive(minSize = 120.dp),
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(Tokens.Spacing.Medium),
                        verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
                        horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
                    ) {
                        when (selectedTab) {
                            0 -> {
                                items(templates.size) { index ->
                                    ExpandedTemplateItem(
                                        itemData = templates[index],
                                        coordinator = coordinator,
                                        onSelect = onDismiss, // 选中后关闭弹窗
                                    )
                                }
                            }
                            1 -> {
                                items(drafts.size) { index ->
                                    ExpandedTemplateItem(
                                        itemData = drafts[index],
                                        coordinator = coordinator,
                                        onSelect = onDismiss,
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

/**
 * 展开模板项 - 用于全屏模板库
 */
@Composable
private fun ExpandedTemplateItem(
    itemData: DraggableItemData,
    coordinator: UnifiedDragCoordinator,
    onSelect: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Surface(
        modifier = modifier
            .fillMaxWidth()
            .height(100.dp),
        color = when (itemData) {
            is DraggableItemData.TemplateData -> MaterialTheme.workoutColors.accentPrimary.copy(alpha = 0.3f)
            is DraggableItemData.DraftData -> MaterialTheme.workoutColors.accentSecondary.copy(alpha = 0.3f)
            else -> MaterialTheme.workoutColors.textSecondary.copy(alpha = 0.2f)
        },
        shape = RoundedCornerShape(Tokens.Radius.Medium),
        onClick = {
            // TODO: 实现选中逻辑
            onSelect()
        },
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(Tokens.Spacing.Medium),
            verticalArrangement = Arrangement.SpaceBetween,
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top,
            ) {
                Text(
                    text = itemData.displayName,
                    style = MaterialTheme.typography.titleSmall,
                    color = MaterialTheme.workoutColors.textPrimary,
                    fontWeight = FontWeight.Medium,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.weight(1f),
                )
                
                Icon(
                    imageVector = when (itemData) {
                        is DraggableItemData.TemplateData -> Icons.Default.FitnessCenter
                        is DraggableItemData.DraftData -> Icons.Default.Edit
                        else -> Icons.Default.FolderOpen
                    },
                    contentDescription = null,
                    tint = MaterialTheme.workoutColors.textSecondary,
                    modifier = Modifier.size(20.dp),
                )
            }
            
            if (itemData.summary.isNotEmpty()) {
                Text(
                    text = itemData.summary,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.workoutColors.textSecondary,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis,
                )
            }
        }
    }
}

/**
 * 统一设计的拖拽源列表
 */
@Composable
private fun <T : DraggableItemData> DragSourceList(
    items: List<T>,
    coordinator: UnifiedDragCoordinator,
    emptyMessage: String,
    modifier: Modifier = Modifier,
) {
    if (items.isEmpty()) {
        // 空状态 - 优化设计
        Box(
            modifier = modifier
                .fillMaxSize()
                .padding(Tokens.Spacing.Large),
            contentAlignment = Alignment.Center,
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
            ) {
                Icon(
                    imageVector = Icons.Default.FolderOpen,
                    contentDescription = null,
                    tint = MaterialTheme.workoutColors.textSecondary.copy(alpha = 0.6f),
                    modifier = Modifier.size(32.dp),
                )
                Text(
                    text = emptyMessage,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.workoutColors.textSecondary,
                    textAlign = androidx.compose.ui.text.style.TextAlign.Center,
                )
            }
        }
    } else {
        // 拖拽项列表 - 优化设计
        LazyColumn(
            modifier = modifier.fillMaxSize(),
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
            contentPadding = PaddingValues(vertical = Tokens.Spacing.Small),
        ) {
            items(items) { item ->
                UnifiedDraggableItem(
                    itemData = item,
                    coordinator = coordinator,
                    modifier = Modifier.fillMaxWidth(),
                )
            }
        }
    }
}

/**
 * 日历式单周画布 - 周一~周日垂直排列
 */
@Composable
private fun WeeklyCalendarCanvas(
    week: Int,
    canvasData: PlanCanvasData,
    coordinator: UnifiedDragCoordinator,
    onCanvasUpdated: (PlanCanvasData) -> Unit,
    modifier: Modifier = Modifier,
) {
    Surface(
        modifier = modifier.padding(Tokens.Spacing.Medium),
        color = MaterialTheme.workoutColors.cardBackground,
        shape = RoundedCornerShape(Tokens.Radius.Large),
        tonalElevation = Tokens.Elevation.Small,
        shadowElevation = Tokens.Elevation.Small,
    ) {
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(Tokens.Spacing.Medium),
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
        ) {
            // 周一到周日，每天一行
            items(7) { dayIndex ->
                val day = dayIndex + 1
                val dayName = getDayName(day)
                val dayDate = getDateForWeekDay(week, day)
                
                DayRow(
                    week = week,
                    day = day,
                    dayName = dayName,
                    dayDate = dayDate,
                    canvasData = canvasData,
                    coordinator = coordinator,
                    onCanvasUpdated = onCanvasUpdated,
                )
            }
        }
    }
}

/**
 * 日期行组件 - 每天的安排
 */
@Composable
private fun DayRow(
    week: Int,
    day: Int,
    dayName: String,
    dayDate: String,
    canvasData: PlanCanvasData,
    coordinator: UnifiedDragCoordinator,
    onCanvasUpdated: (PlanCanvasData) -> Unit,
    modifier: Modifier = Modifier,
) {
    val position = CanvasPosition(week = week, day = day, order = 0)
    val dayItems = canvasData.getItemsForDate(position.getDayIndex())
    
    Surface(
        modifier = modifier.fillMaxWidth(),
        color = if (dayItems.isNotEmpty()) {
            MaterialTheme.workoutColors.accentPrimary.copy(alpha = 0.3f)
        } else {
            MaterialTheme.workoutColors.textSecondary.copy(alpha = 0.1f)
        },
        shape = RoundedCornerShape(Tokens.Radius.Medium),
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Tokens.Spacing.Medium),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically,
        ) {
            // 左侧：日期信息
            Column(
                modifier = Modifier.width(80.dp),
            ) {
                Text(
                    text = dayName,
                    style = MaterialTheme.typography.titleSmall,
                    color = MaterialTheme.workoutColors.textPrimary,
                    fontWeight = FontWeight.Medium,
                )
                Text(
                    text = dayDate,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.workoutColors.textSecondary,
                )
            }
            
            // 中间：训练安排或拖拽区域
            if (dayItems.isNotEmpty()) {
                // 显示已安排的模板
                LazyRow(
                    modifier = Modifier.weight(1f),
                    horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
                ) {
                    items(dayItems) { item ->
                        ScheduledTemplateChip(
                            item = item,
                            onRemove = {
                                // TODO: 实现移除逻辑
                            },
                        )
                    }
                }
            } else {
                // 空状态拖拽区域
                DragDropZone(
                    zoneId = "day_${week}_$day",
                    coordinator = coordinator,
                    modifier = Modifier
                        .weight(1f)
                        .height(40.dp),
                )
            }
            
            // 右侧：快速操作
            IconButton(
                onClick = {
                    // TODO: 实现快速添加模板
                },
            ) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = "添加训练",
                    tint = MaterialTheme.workoutColors.textSecondary,
                )
            }
        }
    }
}

/**
 * 已安排模板芯片
 */
@Composable
private fun ScheduledTemplateChip(
    item: CanvasItem,
    onRemove: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Surface(
        modifier = modifier,
        color = when (item) {
            is CanvasItem.TemplateItem -> MaterialTheme.workoutColors.accentPrimary.copy(alpha = 0.2f)
            is CanvasItem.DraftItem -> MaterialTheme.workoutColors.accentSecondary.copy(alpha = 0.2f)
            else -> MaterialTheme.workoutColors.textSecondary.copy(alpha = 0.2f)
        },
        shape = RoundedCornerShape(Tokens.Radius.Small),
    ) {
        Row(
            modifier = Modifier.padding(
                horizontal = Tokens.Spacing.Small,
                vertical = Tokens.Spacing.XSmall,
            ),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.XSmall),
        ) {
            Text(
                text = item.name,
                style = MaterialTheme.typography.labelMedium,
                color = when (item) {
                    is CanvasItem.TemplateItem -> MaterialTheme.workoutColors.textPrimary
                    is CanvasItem.DraftItem -> MaterialTheme.workoutColors.textPrimary
                    else -> MaterialTheme.workoutColors.textPrimary
                },
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
            )
            
            Icon(
                imageVector = Icons.Default.Close,
                contentDescription = "移除",
                tint = when (item) {
                    is CanvasItem.TemplateItem -> MaterialTheme.workoutColors.textPrimary
                    is CanvasItem.DraftItem -> MaterialTheme.workoutColors.textPrimary
                    else -> MaterialTheme.workoutColors.textPrimary
                },
                modifier = Modifier
                    .size(16.dp)
                    .clip(RoundedCornerShape(8.dp))
                    .clickable { onRemove() },
            )
        }
    }
}

/**
 * 拖拽放置区域
 */
@Composable
private fun DragDropZone(
    zoneId: String,
    coordinator: UnifiedDragCoordinator,
    modifier: Modifier = Modifier,
) {
    Surface(
        modifier = modifier,
        color = MaterialTheme.workoutColors.textSecondary.copy(alpha = 0.1f),
        shape = RoundedCornerShape(Tokens.Radius.Small),
        border = BorderStroke(
            width = 1.dp,
            color = MaterialTheme.workoutColors.textSecondary.copy(alpha = 0.3f),
        ),
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center,
        ) {
            Text(
                text = "拖拽模板到此处",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.workoutColors.textSecondary.copy(alpha = 0.6f),
            )
        }
    }
}

// === 辅助函数 ===

/**
 * 获取周的日期范围显示
 */
private fun getWeekDateRange(week: Int): String {
    // TODO: 实现真实的日期计算
    return when (week) {
        1 -> "7/1-7/7"
        2 -> "7/8-7/14"
        3 -> "7/15-7/21"
        4 -> "7/22-7/28"
        else -> "--"
    }
}

/**
 * 获取天名称
 */
private fun getDayName(day: Int): String {
    return when (day) {
        1 -> "周一"
        2 -> "周二"
        3 -> "周三"
        4 -> "周四"
        5 -> "周五"
        6 -> "周六"
        7 -> "周日"
        else -> "--"
    }
}

/**
 * 获取具体日期
 */
private fun getDateForWeekDay(week: Int, day: Int): String {
    // TODO: 实现真实的日期计算
    val baseDay = (week - 1) * 7 + day
    return "7/$baseDay"
}

/**
 * 将现有Plan数据转换为画布数据
 */
private fun convertPlanToCanvas(
    plan: com.example.gymbro.domain.workout.model.WorkoutPlan,
    baseCanvas: PlanCanvasData,
): PlanCanvasData {
    val scheduleItems = mutableMapOf<Int, List<CanvasItem>>()

    // 转换dailySchedule为CanvasItem
    plan.dailySchedule.forEach { (dayNumber, dayPlan) ->
        val canvasItems = mutableListOf<CanvasItem>()

        // 处理templateVersionIds（如果存在）
        dayPlan.templateVersionIds.forEach { templateVersionId ->
            // TODO: 这里需要通过templateVersionId获取对应的Template数据
            // 暂时创建一个占位的CanvasItem
            val position = CanvasPosition.fromDayIndex(dayNumber, canvasItems.size)
            val placeholderItem = CanvasItem.CustomItem(
                id = "placeholder_$templateVersionId",
                name = "模板引用",
                position = position,
                metadata = mapOf("templateVersionId" to templateVersionId),
                description = "基于模板版本: $templateVersionId",
                estimatedDuration = null,
            )
            canvasItems.add(placeholderItem)
        }

        // 处理templateVersionIds（向后兼容）
        dayPlan.templateVersionIds.forEach { templateVersionId ->
            // TODO: 通过templateVersionId获取Template数据并转换
            val position = CanvasPosition.fromDayIndex(dayNumber, canvasItems.size)
            val placeholderItem = CanvasItem.CustomItem(
                id = "template_ref_$templateVersionId",
                name = "模板",
                position = position,
                metadata = mapOf("templateVersionId" to templateVersionId),
                description = "模板ID: $templateVersionId",
                estimatedDuration = null,
            )
            canvasItems.add(placeholderItem)
        }

        if (canvasItems.isNotEmpty()) {
            scheduleItems[dayNumber] = canvasItems
        }
    }

    return baseCanvas.copy(
        planName = plan.name.toString(),
        scheduleItems = scheduleItems,
    )
}

/**
 * 保存画布数据到Plan
 */
private fun savePlanFromCanvas(
    canvasData: PlanCanvasData,
    planViewModel: PlanViewModel,
    planId: String?,
) {
    // TODO: 实现画布数据到Plan数据的转换
    // 1. 将CanvasItem转换为DayPlan
    // 2. 调用planViewModel.savePlan()

    // 暂时的占位实现
    planViewModel.dispatch(PlanContract.Intent.SavePlan)
}