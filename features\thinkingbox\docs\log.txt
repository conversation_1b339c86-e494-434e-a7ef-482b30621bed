09:06:15.561 CNT-XML-DEBUG            E  🔍 [XML Token检测] messageId=00578fbe-65e8-44c5-872c-492a99ae547c, token='使用杠铃/哑铃避免自由重量失控风险。</phase>'
09:06:15.561 CNT-MESSAGE-FLOW         I  🔥 [TokenRouter] 路由Token: messageId=00578fbe-65e8-44c5-872c-492a99ae547c, token长度=26, 内容预览='使用杠铃/哑铃避免自由重量失控风险。</phase>'
09:06:15.565 CNT-TOKEN-ROUTER         D  🔥 路由Token到messageId=00578fbe-65e8-44c5-872c-492a99ae547c, token长度=26
09:06:15.569 ConversationScope        D  🔍 [XML Token传递] messageId=00578fbe-65e8-44c5-872c-492a99ae547c, token='使用杠铃/哑铃避免自由重量失控风险。</phase>'
09:06:15.572 TB-PARSER                D  📥 [收到Token] #1: '使用杠铃/哑铃避免自由重量失控风险。</phase>'
09:06:15.573 TB-RAW-COL...AGGREGATED  I  🔍 [聚合] 1条消息/2个token: [1][StreamingThinkingMLParser] 使用杠铃/哑铃避免自由重量失控风险。</phase>
09:06:15.573 TB-PARSER                D  🏷️ [关键标签] 检测到重要标签在 token 中
09:06:15.577 TB-XML-INPUT             E  🔍 [XML扫描器输入] 长度=26: '使用杠铃/哑铃避免自由重量失控风险。</phase>'
09:06:15.578 TB-XML-BUFFER            E  🔍 [缓冲区状态] 长度=26, 内容='使用杠铃/哑铃避免自由重量失控风险。</phase>'
09:06:15.580 TB-XML-SCANNER           V  🔍 [标签解析] 尝试解析: '/phase'
09:06:15.580                          V  🔍 [标签解析] 清理后内容: '/phase'
09:06:15.581                          V  🔍 [标签解析] ✅ 识别为关闭标签: 'phase'
09:06:15.584 TB-XML-OUTPUT            E  🔍 [Token输出] 生成2个tokens:
09:06:15.584                          E  🔍 [Token输出] [0] Text(content=使用杠铃/哑铃避免自由重量失控风险。)
09:06:15.585                          E  🔍 [Token输出] [1] TagClose(name=phase)
09:06:15.585 TB-XML-SCANNER           V  🔍 [循环缓冲修复] Feed处理: 输入26字符, 输出2个Token, 缓冲剩余0字符
09:06:15.586 TB-PARSER                V  📝 [文本内容] 使用杠铃/哑铃避免自由重量失控风险。... in state: PRE_THINK
09:06:15.591 TB-VIEWMODEL             D  🔍 [语义事件] TextChunk
09:06:15.595                          D  🔄 [处理ThinkingEvent] SegmentText
09:06:15.595 TB-REDUCER               D  🔄 处理事件: SegmentText
09:06:15.595                          D  📝 追加文本到段[perthink]: 使用杠铃/哑铃避免自由重量失控风险。...
09:06:15.595                          D  📊 状态更新: TBState(current=perthink, queue=0, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
09:06:15.595 TB-CONVERT               D  ✅ 添加当前段到队列: perthink (PERTHINK)
09:06:15.595                          D  📊 Contract状态: 队列=1段, 流式=true, 思考关闭=false
09:06:15.602 TB-PARSER                V  🏷️ [标签关闭] </phase> in state: PRE_THINK
09:06:15.605 TB-VIEWMODEL             D  🔍 [语义事件] TagClosed
09:06:15.606 TB-MAPPER                I  🔚 [finalmermaid规范] </phase> 断点 - 闭合段: perthink (PERTHINK)
09:06:15.607 TB-VIEWMODEL             D  🔄 [处理ThinkingEvent] SegmentClosed
09:06:15.608 TB-REDUCER               D  🔄 处理事件: SegmentClosed
09:06:15.608                          D  🔒 闭合段: perthink
09:06:15.608                          D  📊 状态更新: TBState(current=null, queue=1, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
09:06:15.608 TB-CONVERT               D  📊 Contract状态: 队列=1段, 流式=true, 思考关闭=false
09:06:15.608 ConversationScope        D  🔥 Token发送成功(tryEmit): messageId=00578fbe-65e8-44c5-872c-492a99ae547c, 总计1个tokens
09:06:15.609 CNT-MESSAGE-FLOW         I  🔥 [TokenRouter] Token路由成功，总计路由1个tokens
09:06:15.609 CNT-TOKEN-ROUTER         V  🔥 Token路由成功，总计路由1个tokens
09:06:15.614 TB-DISPLAY               D  ✅ 显示条件满足: 有1个段需要显示
09:06:15.615 TB-RENDER                D  🎯 显示思考内容: perthink (PERTHINK), 内容=18字符
09:06:15.935 TB-UI                    D  ✅ 段渲染完成: perthink
09:06:15.936 TB-CALLBACK              D  📤 段渲染完成回调: perthink
09:06:15.942 TB-REDUCER               D  🔄 处理事件: UiSegmentRendered
09:06:15.942                          D  ✅ [UiSegmentRendered] UI完成渲染段: perthink
09:06:15.943                          D  📊 状态更新: TBState(current=null, queue=0, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
09:06:15.945 TB-CONVERT               D  📊 Contract状态: 队列=0段, 流式=true, 思考关闭=false
09:06:15.949 TB-DISPLAY               D  ✅ 显示条件满足: 等待token或处理中
09:06:15.950 TB-RENDER                D  📱 显示等待状态: ThinkingHeader
09:06:15.966 TB-UI                    D  📊 Segment状态: 队列头段=null, 队列大小=0, 思考关闭=false, 流式=true
09:06:20.707 JSON-STREAM-OUTPUT       I  🚀 [流式输出] messageId=00578fbe-65e8-44c5-872c-492a99ae547c, 内容长度=11
09:06:20.708 CNT-XML-DEBUG            E  🔍 [XML Token检测] messageId=00578fbe-65e8-44c5-872c-492a99ae547c, token='</phase>
                                         </'
09:06:20.708 CNT-MESSAGE-FLOW         I  🔥 [TokenRouter] 路由Token: messageId=00578fbe-65e8-44c5-872c-492a99ae547c, token长度=11, 内容预览='</phase>
                                         </'
09:06:20.708 CNT-TOKEN-ROUTER         D  🔥 路由Token到messageId=00578fbe-65e8-44c5-872c-492a99ae547c, token长度=11
09:06:20.708 ConversationScope        D  🔍 [XML Token传递] messageId=00578fbe-65e8-44c5-872c-492a99ae547c, token='</phase>
                                         </'
09:06:20.709 TB-PARSER                D  📥 [收到Token] #2: '</phase>
                                         </'
09:06:20.709 TB-RAW-COL...AGGREGATED  I  🔍 [聚合] 5条消息/16个token: [EVENT][TB-XML-SCANNER] XMLToken: Text(content=使用杠铃/哑铃避免自由重量失控风险。) [EVENT][processXmlToken] SemanticEvent: TextChunk(text=使用杠铃/哑铃避免自由重量失控风险。) [EVENT][TB-XML-SCANNER] XMLToken: TagClose(name=phase) [EV...
09:06:20.710 TB-PARSER                D  🏷️ [关键标签] 检测到重要标签在 token 中
09:06:20.710 TB-XML-INPUT             E  🔍 [XML扫描器输入] 长度=11: '</phase>
                                         </'
09:06:20.712 TB-XML-BUFFER            E  🔍 [缓冲区状态] 长度=11, 内容='</phase>
                                         </'
09:06:20.712 TB-XML-SCANNER           V  🔍 [标签解析] 尝试解析: '/phase'
09:06:20.712                          V  🔍 [标签解析] 清理后内容: '/phase'
09:06:20.712                          V  🔍 [标签解析] ✅ 识别为关闭标签: 'phase'
09:06:20.713                          V  🔍 [不完整标签] 等待下个chunk: '</'
09:06:20.713 TB-XML-OUTPUT            E  🔍 [Token输出] 生成2个tokens:
09:06:20.713                          E  🔍 [Token输出] [0] TagClose(name=phase)
09:06:20.713                          E  🔍 [Token输出] [1] Text(content=
                                         )
09:06:20.713 TB-XML-SCANNER           V  🔍 [循环缓冲修复] Feed处理: 输入11字符, 输出2个Token, 缓冲剩余2字符
09:06:20.713 TB-PARSER                V  🏷️ [标签关闭] </phase> in state: PRE_THINK
09:06:20.714 TB-VIEWMODEL             D  🔍 [语义事件] TagClosed
09:06:20.714 TB-MAPPER                I  🔚 [兜底机制] </phase>标签结束perthink段
09:06:20.714 TB-VIEWMODEL             D  🔄 [处理ThinkingEvent] SegmentClosed
09:06:20.714 TB-REDUCER               D  🔄 处理事件: SegmentClosed
09:06:20.714                          D  🔒 闭合段: perthink
09:06:20.714                          W  ❌ 段ID不匹配: 期望=null, 实际=perthink
09:06:20.716 ConversationScope        D  🔥 Token发送成功(tryEmit): messageId=00578fbe-65e8-44c5-872c-492a99ae547c, 总计2个tokens
09:06:20.718 CNT-MESSAGE-FLOW         I  🔥 [TokenRouter] Token路由成功，总计路由2个tokens
09:06:20.718 CNT-TOKEN-ROUTER         V  🔥 Token路由成功，总计路由2个tokens
