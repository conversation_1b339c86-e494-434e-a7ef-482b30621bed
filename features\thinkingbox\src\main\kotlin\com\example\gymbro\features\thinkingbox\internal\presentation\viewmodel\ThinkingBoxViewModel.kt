package com.example.gymbro.features.thinkingbox.internal.presentation.viewmodel

import com.example.gymbro.core.arch.mvi.BaseMviViewModel
import com.example.gymbro.core.arch.mvi.Reducer
import com.example.gymbro.features.thinkingbox.internal.contract.ThinkingBoxContract
import com.example.gymbro.features.thinkingbox.internal.effecthandler.ThinkingBoxEffectHandler
import com.example.gymbro.features.thinkingbox.internal.reducer.ThinkingBoxReducer
import dagger.hilt.android.lifecycle.HiltViewModel
import timber.log.Timber
import javax.inject.Inject

/**
 * ThinkingBoxViewModel - MVI 2.0 标准 ViewModel
 *
 * 🎯 核心职责：
 * - 继承 BaseMviViewModel，遵循 MVI 2.0 架构标准
 * - 使用 ThinkingBoxReducer 处理状态变更逻辑
 * - 使用 ThinkingBoxEffectHandler 处理副作用
 * - 支持结构化并发和生命周期管理
 *
 * 🔥 MVI 2.0 合规：
 * - 继承 BaseMviViewModel
 * - 提供 Reducer 实例
 * - 实现 EffectHandler 处理
 * - 使用标准的 dispatch 方法
 * 
 * 🔥 【729方案9优化】重构亮点：
 * - 移除手动状态管理，使用标准 MVI 模式
 * - 分离副作用处理到 EffectHandler
 * - 简化 ViewModel 职责，专注于协调
 * - 支持完整的生命周期管理
 */
@HiltViewModel
class ThinkingBoxViewModel @Inject constructor(
    private val thinkingBoxReducer: ThinkingBoxReducer,
    private val effectHandler: ThinkingBoxEffectHandler,
) : BaseMviViewModel<ThinkingBoxContract.Intent, ThinkingBoxContract.State, ThinkingBoxContract.Effect>(
    initialState = ThinkingBoxContract.State()
) {

    // 提供 Reducer 实例给 BaseMviViewModel
    override val reducer: Reducer<ThinkingBoxContract.Intent, ThinkingBoxContract.State, ThinkingBoxContract.Effect> = thinkingBoxReducer

    init {
        Timber.tag("TB-VIEWMODEL").d("🚀 ThinkingBoxViewModel 初始化")
        initializeEffectHandler()
    }

    /**
     * 初始化 EffectHandler
     * 
     * 🔥 【MVI 2.0 标准】：
     * - 监听 Effects 并委托给 EffectHandler 处理
     * - 通过回调将副作用结果转换为新的 Intent
     * - 使用 handlerScope 确保正确的生命周期管理
     */
    private fun initializeEffectHandler() {
        // 监听 Effects 并使用 EffectHandler 处理
        handlerScope.launch {
            effect.collect { effect ->
                effectHandler.handleEffect(
                    effect = effect,
                    scope = handlerScope,
                    onIntent = { intent ->
                        // 将新的 Intent 分发回 ViewModel
                        dispatch(intent)
                    }
                )
            }
        }
    }

    /**
     * 初始化 ThinkingBox
     *
     * @param messageId 消息ID
     */
    fun initialize(messageId: String) {
        Timber.tag("TB-VIEWMODEL").i("🚀 初始化ThinkingBox: messageId=$messageId")
        dispatch(ThinkingBoxContract.Intent.Initialize(messageId))
    }

    /**
     * 处理 Intent（向后兼容方法）
     *
     * @param intent 用户意图
     */
    fun processIntent(intent: ThinkingBoxContract.Intent) {
        dispatch(intent)
    }

    /**
     * 重置状态
     */
    fun reset() {
        dispatch(ThinkingBoxContract.Intent.Reset)
    }

    /**
     * 清理资源
     * 
     * 🔥 【生命周期管理】：
     * - 调用 super.onCleared() 清理 BaseMviViewModel 资源
     * - 清理 EffectHandler 资源
     * - 确保无内存泄漏
     */
    override fun onCleared() {
        super.onCleared()
        effectHandler.cleanup()
        Timber.tag("TB-VIEWMODEL").d("🧹 ThinkingBoxViewModel 清理完成")
    }
}